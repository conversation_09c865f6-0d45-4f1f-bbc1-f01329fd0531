2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:19:17 - uvicorn.error - INFO - server - _serve:84 - Started server process [125]
2025-08-29 20:19:17 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:19:17 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:19:17 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:19:30 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756469970125_a4ecb507] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756469970125_a4ecb507] image_download_start
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756469970125_a4ecb507] download_start
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756469970125_a4ecb507] download_attempt
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756469970125_a4ecb507] download_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756469970125_a4ecb507] image_download_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756469970125_a4ecb507] image_processing_start
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756469970125_a4ecb507] upload_invoke
2025-08-29 20:19:30 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756469970125_a4ecb507] upload_start
2025-08-29 20:19:30 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:19:30 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:19:30 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756469970125_a4ecb507] upload_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756469970125_a4ecb507] image_processing_done
2025-08-29 20:19:30 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756469970125_a4ecb507] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:19:30 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756469970125_a4ecb507] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756469970583_96270.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A19%3A30Z%2F-1%2F%2Fe319f81dc3319210acbd6dfcc1c81873341638471fd45437aa7fe4385d7bae98"}'}
2025-08-29 20:19:30 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756469970125_a4ecb507] 请求完成: 200 - 耗时: 561ms
2025-08-29 20:20:09 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756470009949_c2fda02d] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:20:09 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756470009949_c2fda02d] image_download_start
2025-08-29 20:20:09 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756470009949_c2fda02d] download_start
2025-08-29 20:20:09 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756470009949_c2fda02d] download_attempt
2025-08-29 20:20:10 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756470009949_c2fda02d] download_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756470009949_c2fda02d] image_download_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756470009949_c2fda02d] image_processing_start
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756470009949_c2fda02d] upload_invoke
2025-08-29 20:20:10 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756470009949_c2fda02d] upload_start
2025-08-29 20:20:10 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756470009949_c2fda02d] upload_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756470009949_c2fda02d] image_processing_done
2025-08-29 20:20:10 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756470009949_c2fda02d] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:20:10 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756470009949_c2fda02d] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470010122_79757.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A20%3A10Z%2F-1%2F%2Fe0e4dd148bd3b7a183381cde96479d8fcf8a9ade0468b6aec71436e7beb4379b"}'}
2025-08-29 20:20:10 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756470009949_c2fda02d] 请求完成: 200 - 耗时: 307ms
2025-08-29 20:23:55 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:23:55 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:23:55 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:23:55 - uvicorn.error - INFO - server - _serve:94 - Finished server process [125]
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:27:45 - uvicorn.error - INFO - server - _serve:84 - Started server process [2236]
2025-08-29 20:27:45 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:27:45 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:27:45 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:27:48 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:27:48 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:27:48 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:27:48 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:27:49 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:27:49 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:27:49 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:27:49 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:27:49 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:27:49 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:27:49 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470469517_56619.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A27%3A49Z%2F-1%2F%2Fc3df2326057f6574c9a591956ce5f081601f72f0b4be57bdad5397289cda7f9f"}'}
2025-08-29 20:27:49 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 721ms
2025-08-29 20:28:38 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:28:38 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:28:38 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:28:38 - uvicorn.error - INFO - server - _serve:94 - Finished server process [2236]
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:28:53 - uvicorn.error - INFO - server - _serve:84 - Started server process [3009]
2025-08-29 20:28:53 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:28:53 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:28:53 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:28:56 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:28:56 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:28:56 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:28:56 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:28:56 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:28:56 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:28:56 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470536795_83609.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A28%3A56Z%2F-1%2F%2Fd7f65bbaa8c50c1f6cbe49e0e6e946d937c1b4a909d0976877aa8a4ea1ad0c2a"}'}
2025-08-29 20:28:56 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 408ms
2025-08-29 20:28:58 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:28:58 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:28:58 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:28:58 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:28:58 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470538274_54391.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A28%3A58Z%2F-1%2F%2Fa05f3262daf2c7a63523d1cccd4a4cda5b56622ca5d752139024eb799862956d"}'}
2025-08-29 20:28:58 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 266ms
2025-08-29 20:31:11 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:31:12 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:31:12 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:31:12 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3009]
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:31:13 - uvicorn.error - INFO - server - _serve:84 - Started server process [3737]
2025-08-29 20:31:13 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:31:13 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:31:13 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:31:15 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:31:15 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:31:16 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:31:16 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:31:16 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:31:16 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:31:16 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:31:16 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470676222_97630.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A31%3A16Z%2F-1%2F%2Fe2686bd917c60f1780e48796d78d427b41bdb90cc736ac6ad8d24735f8c613b4"}'}
2025-08-29 20:31:16 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 486ms
2025-08-29 20:31:21 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:31:21 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3737]
2025-08-29 20:31:21 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/contextlib.py", line 142, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:31:48 - uvicorn.error - INFO - server - _serve:84 - Started server process [4236]
2025-08-29 20:31:48 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:31:48 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:31:48 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:31:51 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:31:51 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:31:51 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:31:51 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:31:52 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:31:52 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:31:52 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:31:52 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:31:52 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:31:52 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:31:52 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470712214_56181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A31%3A52Z%2F-1%2F%2F52c9532b4fb5b0129a9dd05d336ebc5c9b8241483618731adc3e840ce6791090"}'}
2025-08-29 20:31:52 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 418ms
2025-08-29 20:32:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:32:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4236]
2025-08-29 20:32:06 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/contextlib.py", line 142, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

{"time": "2025-08-29 20:32:07", "logger": "visual_tool", "level": "INFO", "message": "使用默认日志配置"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "请求开始: POST /visual_tools/v1/visual_tool", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_download_start", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_start", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "suffix": ".jpeg"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_attempt", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "attempt": 1, "connect_timeout": 0.5, "read_timeout": 5.0}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_success", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_download_d925tfs_.jpeg", "size_kb": 31.93, "attempts": 1, "elapsed_sec": 0.192}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_download_success", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_download_d925tfs_.jpeg", "size_kb": 31.93}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_processing_start", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "width": 554, "height": 554}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_invoke", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_start", "trace_id": "1756470729707_3367b94b", "file_path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37, "prefix": "visual_tools/output"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "Initializing BOS client..."}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "BOS client initialized successfully"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_success", "trace_id": "1756470729707_3367b94b", "file_path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37, "object_key": "visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg", "url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "image_processing_done", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "return_data": false, "url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "响应数据: {'code': 0, 'message': 'Finish', 'data': '{\"type\": \"image_url\", \"image_url\": \"https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305\"}'}", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "请求完成: 200 - 耗时: 498ms", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "access", "level": "INFO", "message": "127.0.0.1 - \"POST /visual_tools/v1/visual_tool\" 200 - 498ms - \"iAPI/1.0.0 (http://iapi.baidu-int.com)\""}
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 21:15:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [15210]
2025-08-29 21:15:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 21:15:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 21:15:09 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 21:15:12 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756473312082_b3633e91] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 21:15:12 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756473312082_b3633e91] image_download_start
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756473312082_b3633e91] download_start
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:12 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:13 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:13 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:85 - [TraceID: 1756473312082_b3633e91] download_failed
2025-08-29 21:15:14 - visual_tool - ERROR - processor - apply_image_tool:80 - [TraceID: 1756473312082_b3633e91] image_download_failed
2025-08-29 21:15:14 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756473312082_b3633e91] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 21:15:14 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756473312082_b3633e91] 请求处理失败: failed to download image_url: HTTPSConnectionPool(host='mcp-env.bj.bcebos.com', port=443): Max retries exceeded with url: /v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x110785f00>, 'Connection to mcp-env.bj.bcebos.com timed out. (connect timeout=0.5)'))
2025-08-29 21:15:14 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756473312082_b3633e91] 请求完成: 200 - 耗时: 2.2s
2025-08-30 09:38:24 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 09:38:25 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 09:38:25 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 09:38:25 - uvicorn.error - INFO - server - _serve:94 - Finished server process [15210]
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:43:22 - uvicorn.error - INFO - server - _serve:84 - Started server process [59574]
2025-08-30 09:43:22 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:43:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:43:22 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:49:22 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756518562340_14bac527] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756518562340_14bac527] image_download_start
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756518562340_14bac527] download_start
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756518562340_14bac527] download_attempt
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756518562340_14bac527] download_success
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756518562340_14bac527] image_download_success
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756518562340_14bac527] image_processing_start
2025-08-30 09:49:23 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756518562340_14bac527] upload_invoke
2025-08-30 09:49:23 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756518562340_14bac527] upload_start
2025-08-30 09:49:23 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:49:23 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:49:23 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756518562340_14bac527] upload_success
2025-08-30 09:49:23 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756518562340_14bac527] image_processing_done
2025-08-30 09:49:23 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756518562340_14bac527] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:49:23 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756518562340_14bac527] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756518563510_55416.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A49%3A23Z%2F-1%2F%2F71dab2108ca363b56a4d15d44e16594b67db04fa215e9fd435cbb7b6d313ac52"}'}
2025-08-30 09:49:23 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756518562340_14bac527] 请求完成: 200 - 耗时: 1.3s
2025-08-30 09:49:45 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 09:49:45 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 09:49:45 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 09:49:45 - uvicorn.error - INFO - server - _serve:94 - Finished server process [59574]
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:50:00 - uvicorn.error - INFO - server - _serve:84 - Started server process [61092]
2025-08-30 09:50:00 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:50:00 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:50:00 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:50:03 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756518603339_08354a03] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756518603339_08354a03] download_start
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756518603339_08354a03] download_attempt
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756518603339_08354a03] download_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756518603339_08354a03] image_download_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756518603339_08354a03] image_processing_start
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756518603339_08354a03] upload_invoke
2025-08-30 09:50:03 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756518603339_08354a03] upload_start
2025-08-30 09:50:03 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:50:03 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:50:03 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756518603339_08354a03] upload_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756518603339_08354a03] image_processing_done
2025-08-30 09:50:03 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756518603339_08354a03] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:50:03 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756518603339_08354a03] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756518603768_35805.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A50%3A03Z%2F-1%2F%2Fd85bfcdcf6455ea11bbc07fbe05040a8f6b8bcf5459a2666fc74a54db7edd4db"}'}
2025-08-30 09:50:03 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756518603339_08354a03] 请求完成: 200 - 耗时: 544ms
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:55:22 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__n3rrk0u.json, suffix=.json
2025-08-30 09:55:22 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__n3rrk0u.json, size_kb=0.42, attempts=1, elapsed_sec=1.19
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zrqpqs04.jpg, suffix=.jpg
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:24 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: url=https://invalid-url-that-does-not-exist.com/test.jpg, attempt=1, error=HTTPSConnectionPool(host='invalid-url-that-does-not-exist.com', port=443): Max retries exceeded with url: /test.jpg (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x101781700>, 'Connection to invalid-url-that-does-not-exist.com timed out. (connect timeout=0.5)'))
2025-08-30 09:55:24 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=2, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:25 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zrqpqs04.jpg, size_kb=177.68, attempts=2, elapsed_sec=2.244
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:55:55 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zp55e2bc.json, suffix=.json
2025-08-30 09:55:55 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/json, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=0.42, attempts=1, elapsed_sec=1.207
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_4977rsjo.jpg, suffix=.jpg
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://invalid-url-that-does-not-exist.com/test.jpg, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:57 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=148.39, attempts=1, elapsed_sec=1.042
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:56:19 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_une4sczx.json, suffix=.json
2025-08-30 09:56:19 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/json, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=0.42, attempts=1, elapsed_sec=0.923
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/status/404, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__79d_q9p.jpg, suffix=.jpg
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:21 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=1, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:21 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=2, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:23 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=2, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:23 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=3, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:67 - download_failed: attempts=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/util/download_util.py", line 52, in download_to_tmp_path
    resp.raise_for_status()
  File "/Users/<USER>/.local/share/mise/installs/python/3.12.11/lib/python3.12/site-packages/requests/models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:57:47 - uvicorn.error - INFO - server - _serve:84 - Started server process [63495]
2025-08-30 09:57:47 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:57:47 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:57:47 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:57:51 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756519071063_7db51502] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756519071063_7db51502] image_download_start
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756519071063_7db51502] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_qdtvkkw3.jpeg, suffix=.jpeg
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:50 - [TraceID: 1756519071063_7db51502] download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:57 - [TraceID: 1756519071063_7db51502] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.14
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756519071063_7db51502] image_download_success
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756519071063_7db51502] image_processing_start
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756519071063_7db51502] upload_invoke
2025-08-30 09:57:51 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756519071063_7db51502] upload_start
2025-08-30 09:57:51 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:57:51 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:57:51 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756519071063_7db51502] upload_success
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756519071063_7db51502] image_processing_done
2025-08-30 09:57:51 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756519071063_7db51502] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:57:51 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756519071063_7db51502] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756519071363_68067.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A57%3A51Z%2F-1%2F%2F30df965791c30f5247f697a186fe8acdac95c0d728191ec8192ef56e8737eb15"}'}
2025-08-30 09:57:51 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756519071063_7db51502] 请求完成: 200 - 耗时: 433ms
2025-08-30 10:05:18 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:05:18 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 10:05:18 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 10:05:18 - uvicorn.error - INFO - server - _serve:94 - Finished server process [63495]
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 10:05:27 - uvicorn.error - INFO - server - _serve:84 - Started server process [66222]
2025-08-30 10:05:27 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 10:05:27 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 10:05:27 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 10:05:30 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756519530412_08f1bf86] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756519530412_08f1bf86] image_download_start
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756519530412_08f1bf86] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_mafiaugo.jpeg, suffix=.jpeg
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:50 - [TraceID: 1756519530412_08f1bf86] download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:57 - [TraceID: 1756519530412_08f1bf86] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.175
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756519530412_08f1bf86] image_download_success
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756519530412_08f1bf86] image_processing_start: tool={name}, width= {w}, height= {h}
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:250 - [TraceID: 1756519530412_08f1bf86] upload_invoke
2025-08-30 10:05:30 - visual_tool - INFO - upload_util - upload_file_to_bos:30 - [TraceID: 1756519530412_08f1bf86] upload_start
2025-08-30 10:05:30 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 10:05:30 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 10:05:30 - visual_tool - INFO - upload_util - upload_file_to_bos:49 - [TraceID: 1756519530412_08f1bf86] upload_success
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:261 - [TraceID: 1756519530412_08f1bf86] image_processing_done
2025-08-30 10:05:30 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756519530412_08f1bf86] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:05:30 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756519530412_08f1bf86] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756519530742_45579.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A05%3A30Z%2F-1%2F%2F4141fdd87933c981358495b81ee8c6caf9578f0e981c45408196b2b016b28950"}'}
2025-08-30 10:05:30 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756519530412_08f1bf86] 请求完成: 200 - 耗时: 464ms
2025-08-30 10:19:49 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:19:49 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 10:19:49 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 10:19:49 - uvicorn.error - INFO - server - _serve:94 - Finished server process [66222]
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 10:19:53 - uvicorn.error - INFO - server - _serve:84 - Started server process [68419]
2025-08-30 10:19:53 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 10:19:53 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 10:19:53 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 10:19:56 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756520396105_7333108e] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756520396105_7333108e] image_download_start
2025-08-30 10:19:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756520396105_7333108e] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_jkpbbe3l.jpeg, suffix=.jpeg
2025-08-30 10:19:56 - visual_tool - INFO - download_util - download_to_tmp_path:56 - [TraceID: 1756520396105_7333108e] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.158
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756520396105_7333108e] image_download_success
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756520396105_7333108e] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:243 - [TraceID: 1756520396105_7333108e] upload_invoke
2025-08-30 10:19:56 - visual_tool - INFO - upload_util - upload_file_to_bos:37 - [TraceID: 1756520396105_7333108e] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmpyri25yys.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520396401_34370.jpeg, size_kb=19.37
2025-08-30 10:19:56 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 10:19:56 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 10:19:56 - visual_tool - INFO - upload_util - upload_file_to_bos:43 - [TraceID: 1756520396105_7333108e] upload_success
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:247 - [TraceID: 1756520396105_7333108e] image_processing_done
2025-08-30 10:19:56 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756520396105_7333108e] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:19:56 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756520396105_7333108e] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520396401_34370.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A19%3A56Z%2F-1%2F%2F2eda7b0d26c4dbc8cb18e2bb0219f55aefea5546fe03f8bffdcec2edbd63d13f"}'}
2025-08-30 10:19:56 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756520396105_7333108e] 请求完成: 200 - 耗时: 433ms
2025-08-30 10:20:00 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756520400870_49054457] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:20:00 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756520400870_49054457] image_download_start
2025-08-30 10:20:00 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756520400870_49054457] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_p09q65t3.jpeg, suffix=.jpeg
2025-08-30 10:20:01 - visual_tool - INFO - download_util - download_to_tmp_path:56 - [TraceID: 1756520400870_49054457] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.159
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756520400870_49054457] image_download_success
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756520400870_49054457] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:243 - [TraceID: 1756520400870_49054457] upload_invoke
2025-08-30 10:20:01 - visual_tool - INFO - upload_util - upload_file_to_bos:37 - [TraceID: 1756520400870_49054457] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp6wgfxc9n.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520401080_93892.jpeg, size_kb=19.37
2025-08-30 10:20:01 - visual_tool - INFO - upload_util - upload_file_to_bos:43 - [TraceID: 1756520400870_49054457] upload_success
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:247 - [TraceID: 1756520400870_49054457] image_processing_done
2025-08-30 10:20:01 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756520400870_49054457] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:20:01 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756520400870_49054457] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520401080_93892.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A20%3A01Z%2F-1%2F%2F1aab23db6b8ad164afb9f2df2e8fb3c3e8968734e026b69c4653a996b78db48a"}'}
2025-08-30 10:20:01 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756520400870_49054457] 请求完成: 200 - 耗时: 465ms
2025-08-30 10:20:50 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:20:50 - uvicorn.error - INFO - server - _serve:94 - Finished server process [68419]
2025-08-30 10:20:50 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 608, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 1936, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

