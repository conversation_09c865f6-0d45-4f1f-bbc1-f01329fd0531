#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""日志查看工具。

用于查看和过滤TraceID相关的日志。
"""

import os
import re
import sys
from datetime import datetime


def view_logs_by_trace_id(trace_id, log_file="logs/app.log"):
    """根据TraceID查看相关日志。
    
    参数:
        trace_id (str): 要查找的TraceID
        log_file (str): 日志文件路径
    """
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"=== 查找TraceID: {trace_id} 的相关日志 ===")
    print(f"日志文件: {log_file}")
    print()
    
    found_lines = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if trace_id in line:
                    found_lines.append((line_num, line.strip()))
    
        if found_lines:
            print(f"找到 {len(found_lines)} 条相关日志:")
            print("-" * 80)
            for line_num, line in found_lines:
                print(f"[{line_num:4d}] {line}")
        else:
            print(f"未找到TraceID '{trace_id}' 的相关日志")
            
    except Exception as e:
        print(f"读取日志文件失败: {e}")


def view_recent_logs(lines=50, log_file="logs/app.log"):
    """查看最近的日志。
    
    参数:
        lines (int): 显示的行数
        log_file (str): 日志文件路径
    """
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"=== 最近 {lines} 行日志 ===")
    print(f"日志文件: {log_file}")
    print()
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:]
            
            for i, line in enumerate(recent_lines, len(all_lines) - len(recent_lines) + 1):
                print(f"[{i:4d}] {line.rstrip()}")
                
    except Exception as e:
        print(f"读取日志文件失败: {e}")


def extract_trace_ids(log_file="logs/app.log"):
    """提取日志中的所有TraceID。
    
    参数:
        log_file (str): 日志文件路径
    """
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"=== 提取所有TraceID ===")
    print(f"日志文件: {log_file}")
    print()
    
    trace_ids = set()
    trace_pattern = re.compile(r'\[TraceID: ([^\]]+)\]')
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                matches = trace_pattern.findall(line)
                trace_ids.update(matches)
        
        if trace_ids:
            print(f"找到 {len(trace_ids)} 个不同的TraceID:")
            for trace_id in sorted(trace_ids):
                print(f"  - {trace_id}")
        else:
            print("未找到任何TraceID")
            
    except Exception as e:
        print(f"读取日志文件失败: {e}")


def main():
    """主函数。"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python view_logs.py recent [lines]     # 查看最近的日志")
        print("  python view_logs.py trace <trace_id>   # 根据TraceID查看日志")
        print("  python view_logs.py list               # 列出所有TraceID")
        print()
        print("示例:")
        print("  python view_logs.py recent 100")
        print("  python view_logs.py trace test_trace_12345")
        print("  python view_logs.py list")
        return
    
    command = sys.argv[1]
    
    if command == "recent":
        lines = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        view_recent_logs(lines)
    elif command == "trace":
        if len(sys.argv) < 3:
            print("请提供TraceID")
            return
        trace_id = sys.argv[2]
        view_logs_by_trace_id(trace_id)
    elif command == "list":
        extract_trace_ids()
    else:
        print(f"未知命令: {command}")


if __name__ == "__main__":
    main()

 # 查看最近日志
# python view_logs.py recent 50

# 根据TraceID查看特定请求的所有日志
# python view_logs.py trace my_custom_trace_123

# 列出所有TraceID
# python view_logs.py list