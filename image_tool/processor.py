"""
图像工具处理模块。

本模块提供图像处理工具的执行逻辑，包括裁剪、旋转、缩放等操作。
本模块仅关注图像处理逻辑，不包含任何与HTTP请求相关的代码。
本模块的所有函数和类都应为静态函数和类，不包含任何实例状态。
"""

import os
import time
import tempfile
import base64
from io import BytesIO
from typing import Dict, Any, Tuple

import numpy
import cv2
from PIL import Image, ImageEnhance
import matplotlib.pyplot as plt
from util.trace_util import get_trace_logger
from util.upload_util import upload_file_to_bos
from util.download_util import download_to_tmp_path
from util.image_util import maybe_resize_bbox, get_abs_coord_from_rel_coord

SUPPORTED_IMAGE_TOOLS = {
    "image_crop_by_rel_coord_tool",
    "image_zoom_in_tool",  # will be mapped before call
    "image_crop_by_abs_coord_tool",
    "image_rotate_tool",
    "image_resize_tool",
    "image_flip_tool",
    "image_brightness_tool",
    "image_contrast_tool",
    "image_color_tool",
    "image_sharpness_tool",
    "image_calchist_tool",
    "image_edge_det_tool",
    "image_smoothing_tool",
    "image_contour_det_tool",
    "image_face_det_tool",
}


def get_from_args(args: Dict[str, Any], key: str, tool_name: str):
    """从参数中获取指定键的值，如果值不存在或为None，则引发ValueError异常。"""
    if key not in args or args.get(key) is None:
        raise ValueError(f"Missing required argument '{key}' for {tool_name}")
    return args[key]


def _ensure_tmp() -> str:
    """确保临时目录存在，并返回其路径。"""
    date_dir = time.strftime("%Y-%m-%d")
    tmp_visual_base = "./tmp"
    tmp_visual_dir = os.path.join(tmp_visual_base, date_dir)
    os.makedirs(tmp_visual_dir, exist_ok=True)
    return tmp_visual_dir


def apply_image_tool(
    name: str, args: Dict[str, Any], extra_params: Dict[str, Any]
) -> Tuple[str, Any]:
    """执行图像工具，并返回 (type, data) 供 make_resp 使用。"""
    logger = get_trace_logger("visual_tool")

    # 获取输入
    image_url = extra_params.get("image_url")
    image_data_b64 = extra_params.get("image_data")
    return_data = bool(extra_params.get("return_data", False))

    if not image_url and not image_data_b64:
        raise ValueError(
            "either extra_params.image_url or extra_params.image_data is required for image tools"
        )

    input_tmp_path = None
    img = None
    input_ext = ".png"
    if image_data_b64:
        try:
            raw = base64.b64decode(image_data_b64)
            img = Image.open(BytesIO(raw))
        except Exception as e:
            raise ValueError(f"invalid image_data base64: {e}")
    elif image_url:
        try:
            logger.info("image_download_start")
            input_tmp_path = download_to_tmp_path(image_url, default_suffix=".png")
            img = Image.open(input_tmp_path)
            input_ext = os.path.splitext(input_tmp_path)[1] or ".png"
            logger.info("image_download_success")
        except Exception as e:
            logger.error("image_download_failed")
            raise ValueError(f"failed to download image_url: {e}")

    # 生成输出临时路径
    tmp_dir = _ensure_tmp()
    fd, output_path = tempfile.mkstemp(suffix=input_ext, prefix="_tmp", dir=tmp_dir)
    os.close(fd)

    # 工具名前置转换：image_zoom_in_tool -> 对应裁剪工具
    if name == "image_zoom_in_tool":
        norm = args.get("norm", True)
        name = (
            "image_crop_by_rel_coord_tool" if norm else "image_crop_by_abs_coord_tool"
        )

    # 记录图片信息与处理开始
    try:
        if img is not None:
            w, h = img.size
            logger.info(f"image_processing_start: tool={name}, width= {w}, height= {h}")
        # 按名称执行工具逻辑
        if name == "image_crop_by_rel_coord_tool":
            bbox_rel = get_from_args(args, "bbox_2d", name)
            if not isinstance(bbox_rel, list) or len(bbox_rel) != 4:
                raise ValueError("argument bbox_2d must be tuple with 4 elements")
            bbox_abs = get_abs_coord_from_rel_coord(*bbox_rel, *img.size)
            bbox = maybe_resize_bbox(*bbox_abs, *img.size)
            if not bbox:
                raise ValueError("ZOOM IN ARGUMENTS ARE INVALID")
            img.crop(bbox).save(output_path)

        elif name == "image_crop_by_abs_coord_tool":
            bbox = get_from_args(args, "bbox_2d", name)
            if not isinstance(bbox, list) or len(bbox) != 4:
                raise ValueError("argument bbox_2d must be number list with 4 elements")
            bbox = maybe_resize_bbox(*bbox, *img.size)
            if not bbox:
                raise ValueError("ZOOM IN ARGUMENTS ARE INVALID")
            img.crop(bbox).save(output_path)

        elif name == "image_rotate_tool":
            angle = get_from_args(args, "angle", name)
            if angle is None or not isinstance(angle, (int, float)):
                raise ValueError("argument angle must be number")
            img.rotate(angle).save(output_path)

        elif name == "image_resize_tool":
            size_new = get_from_args(args, "size", name)
            if not isinstance(size_new, list) or len(size_new) != 2:
                raise ValueError("argument size must be number list with 2 elements")
            if size_new[0] <= 0 or size_new[1] <= 0:
                raise ValueError("argument size must be positive")
            img.resize(size_new).save(output_path)

        elif name == "image_flip_tool":
            flip = get_from_args(args, "flip", name)
            if flip not in ["horizontal", "vertical"]:
                raise ValueError(
                    "argument flip is invalid, must be one of horizontal, vertical"
                )
            if flip == "horizontal":
                img.transpose(Image.FLIP_LEFT_RIGHT).save(output_path)
            else:
                img.transpose(Image.FLIP_TOP_BOTTOM).save(output_path)

        elif name == "image_brightness_tool":
            factor = get_from_args(args, "factor", name)
            if factor is None or not isinstance(factor, (int, float)):
                raise ValueError("argument factor must be number")
            if factor < 0:
                raise ValueError("argument factor must be positive")
            ImageEnhance.Brightness(img).enhance(factor).save(output_path)

        elif name == "image_contrast_tool":
            factor = get_from_args(args, "factor", name)
            if factor is None or not isinstance(factor, (int, float)):
                raise ValueError("argument factor must be number")
            if factor < 0:
                raise ValueError("argument factor must be positive")
            ImageEnhance.Contrast(img).enhance(factor).save(output_path)

        elif name == "image_color_tool":
            factor = get_from_args(args, "factor", name)
            if factor is None or not isinstance(factor, (int, float)):
                raise ValueError("argument factor must be number")
            if factor < 0:
                raise ValueError("argument factor must be positive")
            ImageEnhance.Color(img).enhance(factor).save(output_path)

        elif name == "image_sharpness_tool":
            factor = get_from_args(args, "factor", name)
            if factor is None or not isinstance(factor, (int, float)):
                raise ValueError("argument factor must be number")
            if factor < 0:
                raise ValueError("argument factor must be positive")
            ImageEnhance.Sharpness(img).enhance(factor).save(output_path)

        elif name == "image_calchist_tool":
            img_bgr = cv2.cvtColor(numpy.asarray(img), cv2.COLOR_RGB2BGR)
            channels = get_from_args(args, "channels", name)
            if len(channels) != 1 or channels[0] not in [0, 1, 2]:
                raise ValueError(
                    "argument channels array and only must be one of 0, 1, 2"
                )
            hist = cv2.calcHist([img_bgr], channels, None, [256], [0, 256])
            plt.plot(hist)
            plt.title("Histogram")
            plt.xlabel("Pixel Value")
            plt.ylabel("Frequency")
            plt.savefig(output_path)
            plt.close()

        elif name == "image_edge_det_tool":
            img_gray = cv2.cvtColor(numpy.array(img), cv2.COLOR_RGB2GRAY)
            threshold1 = get_from_args(args, "threshold1", name)
            threshold2 = get_from_args(args, "threshold2", name)
            edges = cv2.Canny(
                img_gray, threshold1, threshold2, apertureSize=3, L2gradient=False
            )
            cv2.imwrite(output_path, edges)

        elif name == "image_smoothing_tool":
            img_bgr = cv2.cvtColor(numpy.asarray(img), cv2.COLOR_RGB2BGR)
            kernel = get_from_args(args, "kernel", name)
            blurred_image = cv2.GaussianBlur(img_bgr, kernel, 0)
            cv2.imwrite(output_path, blurred_image)

        elif name == "image_contour_det_tool":
            img_gray = cv2.cvtColor(numpy.asarray(img), cv2.COLOR_RGB2GRAY)
            _, binary = cv2.threshold(img_gray, 127, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(
                binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE
            )
            output_image = numpy.zeros_like(img_gray)
            cv2.drawContours(output_image, contours, -1, (255, 0, 0), 2)
            cv2.imwrite(output_path, output_image)

        elif name == "image_face_det_tool":
            img_bgr = cv2.cvtColor(numpy.asarray(img), cv2.COLOR_RGB2BGR)
            img_gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
            face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + "haarcascade_frontalface_default.xml"
            )
            faces = face_cascade.detectMultiScale(
                img_gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            for x, y, w, h in faces:
                cv2.rectangle(img_bgr, (x, y), (x + w, y + h), (255, 0, 0), 2)
            cv2.imwrite(output_path, img_bgr)

        else:
            raise ValueError(f"Unknown tool name: {name}")

        # 输出处理
        if return_data:
            with open(output_path, "rb") as f:
                b64 = base64.b64encode(f.read()).decode("utf-8")
            logger.info("image_processing_done")
            return ("image_url", b64)
        else:
            logger.info("upload_invoke")
            url = upload_file_to_bos(
                output_path, object_key_prefix="visual_tools/output"
            )
            logger.info("image_processing_done")
            return ("image_url", url)

    finally:
        try:
            if os.path.exists(output_path):
                os.unlink(output_path)
        except Exception:
            pass
        try:
            if input_tmp_path and os.path.exists(input_tmp_path):
                os.unlink(input_tmp_path)
        except Exception:
            pass
