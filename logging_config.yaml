version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  detailed:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  access:
    format: "%(asctime)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  app_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  access_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: access
    filename: logs/access.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

loggers:
  visual_tool:
    level: INFO
    handlers: [console, app_file, error_file]
    propagate: false

  access:
    level: INFO
    handlers: [access_file]
    propagate: false

  uvicorn:
    level: INFO
    handlers: [console, app_file]
    propagate: false

  uvicorn.access:
    level: INFO
    handlers: [access_file]
    propagate: false

root:
  level: INFO
  handlers: [console, app_file]
