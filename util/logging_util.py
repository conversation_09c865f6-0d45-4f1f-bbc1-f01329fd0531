# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""日志配置工具模块。

本模块提供日志系统的初始化和配置功能。
"""

import os
import logging
import logging.config
import yaml
from pathlib import Path


def setup_logging(config_path: str = "logging_config.yaml", logs_dir: str = "logs"):
    """设置日志配置。
    
    参数:
        config_path (str): 日志配置文件路径，默认为 "logging_config.yaml"
        logs_dir (str): 日志文件目录，默认为 "logs"
    """
    # 创建日志目录
    Path(logs_dir).mkdir(exist_ok=True)
    
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        # 如果配置文件不存在，使用默认配置
        setup_default_logging(logs_dir)
        return
    
    try:
        # 加载YAML配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 应用日志配置
        logging.config.dictConfig(config)
        
        # 获取logger并记录启动信息
        logger = logging.getLogger("visual_tool")
        logger.info("日志系统初始化完成")
        logger.info(f"日志配置文件: {config_path}")
        logger.info(f"日志目录: {logs_dir}")
        
    except Exception as e:
        # 如果配置失败，回退到默认配置
        print(f"日志配置加载失败: {e}")
        setup_default_logging(logs_dir)


def setup_default_logging(logs_dir: str = "logs"):
    """设置默认日志配置。
    
    参数:
        logs_dir (str): 日志文件目录
    """
    # 创建日志目录
    Path(logs_dir).mkdir(exist_ok=True)
    
    # 默认日志配置
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f"{logs_dir}/app.log", encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger("visual_tool")
    logger.info("使用默认日志配置")


def get_logger(name: str = "visual_tool") -> logging.Logger:
    """获取指定名称的logger。
    
    参数:
        name (str): logger名称，默认为 "visual_tool"
    
    返回:
        logging.Logger: logger实例
    """
    return logging.getLogger(name)


def get_access_logger() -> logging.Logger:
    """获取访问日志logger。
    
    返回:
        logging.Logger: 访问日志logger实例
    """
    return logging.getLogger("access")
