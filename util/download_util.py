"""下载工具模块。

本模块提供下载URL到临时文件的工具函数。
"""
import os
import time
import tempfile
from typing import Optional

import requests
from util.trace_util import get_trace_logger


def _ensure_tmp() -> str:
    date_dir = time.strftime("%Y-%m-%d")
    tmp_visual_base = "./tmp"
    tmp_visual_dir = os.path.join(tmp_visual_base, date_dir)
    os.makedirs(tmp_visual_dir, exist_ok=True)
    return tmp_visual_dir


def download_to_tmp_path(url: str, default_suffix: str = ".tmp") -> str:
    """下载URL到临时文件并返回文件路径。

    - 最多3次重试，递增延迟（0.2s, 0.4s, 0.6s）
    - 超时：连接0.5s，读取5.0s
    - 扩展名：优先从URL推断，否则使用 default_suffix
    - 临时文件：使用 _ensure_tmp() + tempfile.mkstemp
    """
    logger = get_trace_logger("visual_tool")

    connect_timeout = 0.5
    read_timeout = 5.0
    max_retries = 3

    # 推断扩展名
    try:
        path_part = url.split("?")[0]
    except Exception:
        path_part = url
    ext = os.path.splitext(path_part)[1]
    suffix = ext or default_suffix

    tmp_dir = _ensure_tmp()
    fd, path = tempfile.mkstemp(suffix=suffix, prefix="_tmp_download_", dir=tmp_dir)
    os.close(fd)

    logger.info(f"download_start: url={url}, path={path}, suffix={suffix}")

    last_err: Optional[Exception] = None
    t0 = time.time()
    for attempt in range(max_retries):
        try:
            resp = requests.get(url, timeout=(connect_timeout, read_timeout))
            resp.raise_for_status()
            with open(path, "wb") as f:
                f.write(resp.content)
            size_kb = round(os.path.getsize(path) / 1024.0, 2)
            elapsed = round(time.time() - t0, 3)
            logger.info(
                f"download_success: size_kb={size_kb}, attempts={attempt + 1}, elapsed_sec={elapsed}"
            )
            return path
        except Exception as e:
            last_err = e
            logger.error(
                f"download_attempt_failed: attempt={attempt + 1}, error={str(e)}"
            )
            # 递增延迟
            if attempt < max_retries - 1:
                time.sleep(0.2 * (attempt + 1))

    # 全部失败，抛出最后一次错误
    logger.error(
        f"download_failed: attempts={max_retries}, error={str(last_err) if last_err else None}",
        exc_info=last_err,
    )
    if last_err is not None:
        raise last_err
    # 理论上不会到这里，兜底抛出异常
    raise RuntimeError("download_to_tmp_path failed for unknown reasons")
