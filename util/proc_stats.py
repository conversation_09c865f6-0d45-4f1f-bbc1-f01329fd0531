#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
File : ProcStats.py
Author: liudongxue01(<EMAIL>)
Date: Thu Jan  20 17:20:39 CST 2022
Desc: 系统状态统计
"""

from datetime import datetime
import os
import psutil


class ProcStats(object):
    """系统级统计信息"""

    class ProcRunningTime(object):
        """进程运行时间统计"""

        _begin_time = datetime.now()

        @classmethod
        def start_at(cls):
            """start_at"""
            return cls._begin_time.isoformat()

        @classmethod
        def machine_time(cls):
            """machine_time"""
            return datetime.now().isoformat()

        @classmethod
        def elapsed(cls):
            """elapsed"""
            timespan = datetime.now() - cls._begin_time
            return str(timespan)

    class ProcMemInfo(object):
        """内存信息统计"""

        _pid = os.getpid()

        @classmethod
        def mem_used_mb(cls):
            """内存信息统计"""
            # https://code.google.com/archive/p/psutil/wikis/Documentation.wiki
            # https://psutil.readthedocs.io/en/latest/#process-class
            mem_mb = "N/A"
            p = psutil.Process(cls._pid)
            # memory_full_info > V4.0.0
            if hasattr(p, "memory_full_info"):
                mem_mb = p.memory_full_info().uss / 1024 / 1024
            # get_memory_info in V1.1.0~V1.2.1(?), dropped later
            elif hasattr(p, "get_memory_info"):
                mem_mb = p.get_memory_info().rss / 1024 / 1024
            elif hasattr(p, "memory_info"):
                mem_mb = p.memory_info().rss / 1024 / 1024
            return mem_mb

        @classmethod
        def stats(cls):
            """内存信息统计"""
            m = psutil.virtual_memory()
            s = psutil.swap_memory()

            mem_used_mb = cls.mem_used_mb()
            if mem_used_mb > 1000:
                mem_used = "%.2fMB/%.2fGB" % (mem_used_mb, mem_used_mb / 1024)
            else:
                mem_used = "%.2fMB" % (mem_used_mb)

            stats = {
                "sys": {
                    "virtual": {
                        "total": m.total,
                        "used": m.used,
                        "free": m.free,
                        "percent": m.percent,
                    },
                    "swap": {
                        "total": s.total,
                        "used": s.used,
                        "free": s.free,
                        "percent": s.percent,
                    },
                },
                "proc": {"used": mem_used},
            }
            return stats

    @classmethod
    def stats(self):
        """统计信息"""
        pid = os.getpid()
        start_at = self.ProcRunningTime.start_at()
        machine_time = self.ProcRunningTime.machine_time()
        elapsed = self.ProcRunningTime.elapsed()
        stats = self.ProcMemInfo.stats()
        return {"pid": pid, "start_at": start_at, "machine_time": machine_time, "elapsed": elapsed, "memory": stats}


if __name__ == "__main__":  # pragma: no cover
    import json