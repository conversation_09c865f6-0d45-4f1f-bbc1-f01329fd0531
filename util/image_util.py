# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""图像处理工具模块。

本模块提供图像处理相关的工具函数，包括边界框验证、坐标转换等功能。
"""

import logging
from typing import Dict, Any
from math import ceil, floor

logger = logging.getLogger("visual_tool")


def validate_bbox(left, top, right, bottom):
    """验证边界框坐标和尺寸。

    参数:
        left (float): 边界框的左坐标。
        top (float): 边界框的上坐标。
        right (float): 边界框的右坐标。
        bottom (float): 边界框的下坐标。

    返回:
        bool: 如果边界框有效返回True，否则返回False。
    """
    try:
        assert (
            left < right and bottom > top
        ), f"invalid shape for {left=}, {top=}, {right=}, {bottom=}"
        height = bottom - top
        width = right - left
        assert (
            max(height, width) / min(height, width) <= 100
        ), f"aspect ratio error: {left=}, {top=}, {right=}, {bottom=}"
        # assert min(height, width) > 30, f"{height=}, {width=} is too small"
        return True
    except Exception as err:
        return False


def maybe_resize_bbox(left, top, right, bottom, img_width, img_height):
    """如果边界框太小则调整其大小，确保最小尺寸。

    参数:
        left (float): 边界框的左坐标。
        top (float): 边界框的上坐标。
        right (float): 边界框的右坐标。
        bottom (float): 边界框的下坐标。
        img_width (int): 图像的宽度。
        img_height (int): 图像的高度。

    返回:
        list or None: 调整后的边界框坐标 [left, top, right, bottom] 或 None（如果无效）。
    """
    left = max(0, left)
    top = max(0, top)
    right = min(img_width, right)
    bottom = min(img_height, bottom)
    if not validate_bbox(left, top, right, bottom):
        return None

    height = bottom - top
    width = right - left
    if height < 28 or width < 28:
        logger.warning("cropped image is too small, resize it")
        center_x = (left + right) / 2.0
        center_y = (top + bottom) / 2.0
        ratio = 28 / min(height, width)
        new_half_height = ceil(height * ratio * 0.5)
        new_half_width = ceil(width * ratio * 0.5)
        new_left = floor(center_x - new_half_width)
        new_right = ceil(center_x + new_half_width)
        new_top = floor(center_y - new_half_height)
        new_bottom = ceil(center_y + new_half_height)
        if not validate_bbox(new_left, new_top, new_right, new_bottom):
            return None
        return [new_left, new_top, new_right, new_bottom]
    return [left, top, right, bottom]


def get_abs_coord_from_rel_coord(left, top, right, bottom, img_width, img_height):
    """将相对坐标（0-1000）转换为绝对像素坐标。

    参数:
        left (float): 相对单位（0-1000）的左坐标。
        top (float): 相对单位（0-1000）的上坐标。
        right (float): 相对单位（0-1000）的右坐标。
        bottom (float): 相对单位（0-1000）的下坐标。
        img_width (int): 图像的像素宽度。
        img_height (int): 图像的像素高度。

    返回:
        list: 像素单位的绝对坐标 [left, top, right, bottom]。
    """
    left = left / 1000 * img_width
    right = right / 1000 * img_width
    top = top / 1000 * img_height
    bottom = bottom / 1000 * img_height

    left = max(0, min(left, img_width))
    top = max(0, min(top, img_height))
    right = max(left + 1, min(right, img_width))
    bottom = max(top + 1, min(bottom, img_height))
    return [left, top, right, bottom]