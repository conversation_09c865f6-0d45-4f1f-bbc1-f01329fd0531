# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""追踪工具模块。

本模块提供TraceID生成、上下文管理和日志关联功能。
"""

import uuid
import time
import contextvars
import logging
from typing import Optional, Any, Dict
from functools import wraps

# 创建上下文变量来存储TraceID
trace_id_var: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('trace_id', default=None)


def generate_trace_id() -> str:
    """生成唯一的TraceID。
    
    返回:
        str: 格式为 timestamp_uuid 的TraceID
    """
    timestamp = int(time.time() * 1000)  # 毫秒时间戳
    unique_id = str(uuid.uuid4()).replace('-', '')[:8]  # 8位UUID
    return f"{timestamp}_{unique_id}"


def set_trace_id(trace_id: str) -> None:
    """设置当前上下文的TraceID。
    
    参数:
        trace_id (str): 要设置的TraceID
    """
    trace_id_var.set(trace_id)


def get_trace_id() -> Optional[str]:
    """获取当前上下文的TraceID。
    
    返回:
        Optional[str]: 当前的TraceID，如果没有则返回None
    """
    return trace_id_var.get()


def get_or_create_trace_id() -> str:
    """获取或创建TraceID。
    
    如果当前上下文中没有TraceID，则创建一个新的。
    
    返回:
        str: TraceID
    """
    trace_id = get_trace_id()
    if trace_id is None:
        trace_id = generate_trace_id()
        set_trace_id(trace_id)
    return trace_id


class TraceLoggerAdapter(logging.LoggerAdapter):
    """带TraceID的日志适配器。
    
    自动在日志消息中添加TraceID信息。
    """
    
    def process(self, msg, kwargs):
        """处理日志消息，添加TraceID。
        
        参数:
            msg: 日志消息
            kwargs: 日志参数
        
        返回:
            tuple: 处理后的消息和参数
        """
        trace_id = get_trace_id()
        if trace_id:
            msg = f"[TraceID: {trace_id}] {msg}"
        return msg, kwargs


def get_trace_logger(name: str = "visual_tool") -> TraceLoggerAdapter:
    """获取带TraceID的logger。
    
    参数:
        name (str): logger名称
    
    返回:
        TraceLoggerAdapter: 带TraceID的logger适配器
    """
    logger = logging.getLogger(name)
    return TraceLoggerAdapter(logger, {})


def trace_function(func):
    """函数追踪装饰器。
    
    自动记录函数的进入和退出，包括参数和返回值。
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_trace_logger()
        trace_id = get_or_create_trace_id()
        
        # 记录函数进入
        logger.info(f"进入函数 {func.__name__}")
        
        try:
            # 执行函数
            result = func(*args, **kwargs)
            
            # 记录函数成功退出
            logger.info(f"函数 {func.__name__} 执行成功")
            return result
            
        except Exception as e:
            # 记录函数异常
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
    
    return wrapper


def log_request_response(logger: TraceLoggerAdapter, 
                        request_data: Dict[str, Any], 
                        response_data: Dict[str, Any] = None,
                        error: Exception = None) -> None:
    """记录请求和响应数据。
    
    参数:
        logger (TraceLoggerAdapter): 日志记录器
        request_data (Dict[str, Any]): 请求数据
        response_data (Dict[str, Any], optional): 响应数据
        error (Exception, optional): 错误信息
    """
    # 记录请求
    logger.info(f"请求数据: {_sanitize_log_data(request_data)}")
    
    if error:
        # 记录错误响应
        logger.error(f"请求处理失败: {str(error)}")
    elif response_data:
        # 记录成功响应
        logger.info(f"响应数据: {_sanitize_log_data(response_data)}")


def _sanitize_log_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """清理日志数据，移除敏感信息。
    
    参数:
        data (Dict[str, Any]): 原始数据
    
    返回:
        Dict[str, Any]: 清理后的数据
    """
    if not isinstance(data, dict):
        return data
    
    sanitized = {}
    for key, value in data.items():
        if key.lower() in ['image_data', 'image_url']:
            # 对于图像数据，只记录类型和大小信息
            if key == 'image_data' and isinstance(value, str):
                sanitized[key] = f"<base64_data_length:{len(value)}>"
            elif key == 'image_url' and isinstance(value, str):
                sanitized[key] = f"<url:{value[:50]}...>" if len(value) > 50 else value
            else:
                sanitized[key] = f"<{type(value).__name__}>"
        else:
            sanitized[key] = value
    
    return sanitized
