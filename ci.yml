Global:
  version: "2.0"
  group_email: <EMAIL>

Default:
  profile: [publish]

Profiles:
  - profile:
    name: dev
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.10.10
    build:
      command: pip3 install -r requirements.txt && python3 -m build && mv dist output

  - profile:
    name: publish
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.10.10
    build:
      command: pip3 install -r requirements.txt && python3 -m build && mv dist output
